#!/usr/bin/env python3
"""
Example usage of the Tor IP rotation system
"""

from tor_requester import TorManager, TorRequester
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_text_list_from_file(filename: str) -> list:
    """Load text list from a file (one item per line)"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        logger.error(f"File {filename} not found")
        return []
    except Exception as e:
        logger.error(f"Error reading file {filename}: {e}")
        return []

def main():
    """Example usage"""
    
    # Initialize Tor manager with your settings
    tor_manager = TorManager(
        control_port=9051,
        socks_port=9050,
        password=None  # Set to your Tor control password if you have one configured
    )
    
    try:
        # Start Tor service
        logger.info("Starting Tor service...")
        if not tor_manager.start_tor_service():
            logger.error("Failed to start Tor service")
            return
        
        # Verify initial connection
        initial_ip = tor_manager.get_current_ip()
        if not initial_ip:
            logger.error("Cannot connect through Tor. Check your configuration.")
            return
        
        logger.info(f"Connected through Tor. Initial IP: {initial_ip}")
        
        # Initialize requester
        requester = TorRequester(tor_manager)
        
        # Option 1: Use a predefined list
        text_list = [
            "Message 1: Hello World",
            "Message 2: Testing Tor rotation",
            "Message 3: Third message",
            "Message 4: Fourth message", 
            "Message 5: Fifth message",
            "Message 6: Sixth message - IP should change after this",
            "Message 7: Seventh message with new IP",
            "Message 8: Eighth message",
            "Message 9: Ninth message",
            "Message 10: Tenth message",
            "Message 11: Eleventh message - IP should change again"
        ]
        
        # Option 2: Load from file (uncomment to use)
        # text_list = load_text_list_from_file('input_texts.txt')
        
        if not text_list:
            logger.error("No text data to process")
            return
        
        # Your target URL
        target_url = "https://httpbin.org/post"  # Replace with your actual URL
        
        # Process the requests with IP rotation every 5 requests
        logger.info(f"Processing {len(text_list)} requests...")
        requester.process_text_list(text_list, target_url, method='POST')
        
        logger.info("All requests completed!")
        
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"Error: {e}")
    finally:
        # Always cleanup
        logger.info("Stopping Tor service...")
        tor_manager.stop_tor_service()

if __name__ == "__main__":
    main()
