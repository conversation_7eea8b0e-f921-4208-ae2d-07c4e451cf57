# Number Detection in Images - Setup Guide

This guide will help you set up and use the number detection scripts to extract numbers from images like `a.jpg`.

## 📋 Prerequisites

### 1. Install Python Dependencies
```bash
pip install -r requirements_ocr.txt
```

### 2. Install Tesseract OCR

#### Windows:
1. Download Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki
2. Install the executable
3. Add Tesseract to your PATH, or set the path in Python:
```python
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install tesseract-ocr
```

#### macOS:
```bash
brew install tesseract
```

## 🚀 Usage

### Method 1: Simple Detection (Recommended for beginners)

```bash
python simple_number_detector.py
```

This script:
- Uses only Tesseract OCR
- Automatically preprocesses the image
- Extracts all numbers found
- Simple and fast

### Method 2: Advanced Detection (Multiple OCR engines)

```bash
python number_detector.py
```

This script offers:
- Multiple OCR engines (Tesseract + EasyOCR)
- Advanced image preprocessing
- Position detection for numbers
- Better accuracy for complex images

## 📁 File Structure

```
your_project/
├── a.jpg                      # Your input image
├── number_detector.py         # Advanced detection script
├── simple_number_detector.py  # Simple detection script
├── requirements_ocr.txt       # Python dependencies
└── OCR_SETUP_GUIDE.md        # This guide
```

## 🔧 Configuration

### For different image types:
- **Clear text images**: Use `simple_number_detector.py`
- **Noisy/complex images**: Use `number_detector.py` with EasyOCR
- **Handwritten numbers**: Use EasyOCR method
- **Printed numbers**: Tesseract works well

### Tesseract Configuration Options:
```python
# Only digits
custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789'

# Include decimal points
custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789.'

# Different page segmentation modes:
# --psm 6: Uniform block of text
# --psm 7: Single text line
# --psm 8: Single word
# --psm 10: Single character
```

## 📊 Example Usage

### Basic Number Detection:
```python
from simple_number_detector import detect_numbers_simple

# Detect numbers in your image
numbers = detect_numbers_simple("a.jpg")
print(f"Found numbers: {numbers}")
```

### Advanced Detection with Positions:
```python
from number_detector import NumberDetector

# Initialize detector
detector = NumberDetector(method='both')

# Detect numbers
numbers = detector.detect_numbers("a.jpg")
print(f"Detected numbers: {numbers}")

# Get numbers with positions
numbers_with_pos = detector.detect_numbers_with_positions("a.jpg")
for number, bbox in numbers_with_pos:
    print(f"Number: {number}, Position: {bbox}")
```

## 🛠️ Troubleshooting

### Common Issues:

1. **"No numbers detected"**:
   - Check image quality and contrast
   - Try different preprocessing options
   - Ensure numbers are clearly visible

2. **"Tesseract not found"**:
   - Install Tesseract OCR
   - Set correct path in code
   - Verify installation: `tesseract --version`

3. **Poor accuracy**:
   - Try different OCR engines (EasyOCR vs Tesseract)
   - Adjust image preprocessing
   - Use appropriate PSM mode

4. **Import errors**:
   - Install all dependencies: `pip install -r requirements_ocr.txt`
   - Check Python version compatibility

### Performance Tips:

- **For speed**: Use `simple_number_detector.py`
- **For accuracy**: Use `number_detector.py` with both engines
- **For handwriting**: Use EasyOCR method
- **For printed text**: Use Tesseract method

## 📝 Supported Image Formats

- JPG/JPEG
- PNG
- BMP
- TIFF
- GIF (static)

## 🎯 Expected Output

```
Detecting numbers in: a.jpg
Detected numbers: ['123', '456', '789']

✅ Found 3 number(s):
  1. 123
  2. 456
  3. 789
```

## 🔍 Advanced Features

### Image Preprocessing Options:
- Grayscale conversion
- Noise reduction
- Contrast enhancement
- Binary thresholding
- Morphological operations

### Detection Methods:
- **Tesseract**: Fast, good for printed text
- **EasyOCR**: Better for handwriting and complex images
- **Combined**: Uses both for maximum accuracy

## 📞 Support

If you encounter issues:
1. Check that `a.jpg` exists in the same directory
2. Verify all dependencies are installed
3. Test with a simple, clear image first
4. Check Tesseract installation and PATH

Happy number detecting! 🔢
