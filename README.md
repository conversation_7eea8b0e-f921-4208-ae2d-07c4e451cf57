# Tor IP Rotation System

A comprehensive Python system for managing Tor service, rotating IP addresses, and sending HTTP requests through Tor proxy.

## Features

- **Automatic Tor Service Management**: Start/stop Tor service programmatically
- **IP Rotation**: Change Tor IP address on demand
- **Request Management**: Send HTTP requests through Tor proxy
- **Batch Processing**: Process lists of text data with automatic IP rotation
- **Configurable Rotation**: Change IP every N requests (default: 5)
- **Logging**: Comprehensive logging for monitoring and debugging

## Requirements

- Python 3.7+
- Tor installed on your system
- Required Python packages (see requirements.txt)

## Installation

1. **Install Tor**:
   - **Windows**: Download from https://www.torproject.org/download/
   - **Linux**: `sudo apt-get install tor`
   - **macOS**: `brew install tor`

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Tor** (if needed):
   - Edit your Tor configuration file (torrc)
   - Ensure ControlPort is enabled: `ControlPort 9051`
   - Set a control password: `HashedControlPassword <your_hashed_password>`

## Usage

### Basic Usage

```python
from tor_requester import TorManager, TorRequester

# Initialize Tor manager
tor_manager = TorManager(
    control_port=9051,
    socks_port=9050,
    password="your_control_password"
)

# Start Tor service
tor_manager.start_tor_service()

# Get current IP
current_ip = tor_manager.get_current_ip()
print(f"Current IP: {current_ip}")

# Change IP
tor_manager.change_ip()

# Initialize requester
requester = TorRequester(tor_manager)

# Send a single request
response = requester.send_request("https://httpbin.org/post", "test data")

# Process multiple requests with IP rotation
text_list = ["data1", "data2", "data3", "data4", "data5", "data6"]
requester.process_text_list(text_list, "https://your-target-url.com")

# Cleanup
tor_manager.stop_tor_service()
```

### Running the Example

```bash
python example_usage.py
```

### Using with Text File Input

Create a file called `input_texts.txt` with one text item per line:
```
First message
Second message
Third message
...
```

Then modify `example_usage.py` to load from file:
```python
text_list = load_text_list_from_file('input_texts.txt')
```

## Configuration

### TorManager Parameters

- `control_port`: Tor control port (default: 9051)
- `socks_port`: Tor SOCKS proxy port (default: 9050)
- `password`: Tor control password (default: "erfangmail")

### TorRequester Parameters

- `ip_change_interval`: Number of requests before IP change (default: 5)

## Key Functions

### TorManager

- `start_tor_service()`: Start Tor service
- `stop_tor_service()`: Stop Tor service
- `change_ip()`: Request new IP address
- `get_current_ip()`: Get current external IP
- `is_tor_running()`: Check if Tor is running

### TorRequester

- `send_request(url, data, method)`: Send single HTTP request
- `process_text_list(text_list, target_url, method)`: Process multiple requests with IP rotation

## Troubleshooting

1. **Tor won't start**: 
   - Check if Tor is installed
   - Verify ports 9050 and 9051 are available
   - Check Tor configuration

2. **Authentication failed**:
   - Verify control password is correct
   - Check Tor control port configuration

3. **IP not changing**:
   - Ensure sufficient time between requests
   - Check Tor logs for errors
   - Verify control connection is working

4. **Requests failing**:
   - Check target URL is accessible
   - Verify proxy configuration
   - Check firewall settings

## Security Notes

- This tool is for educational and legitimate testing purposes only
- Respect website terms of service and rate limits
- Use responsibly and ethically
- Consider the legal implications in your jurisdiction

## License

This project is for educational purposes. Use responsibly.
