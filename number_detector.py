#!/usr/bin/env python3
"""
Number Detection in Images using OCR
Supports multiple methods: Tesseract OCR, EasyOCR, and OpenCV preprocessing
"""

import cv2
import numpy as np
import re
import logging
from typing import List, Tuple, Optional
import os
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NumberDetector:
    """Class to detect and extract numbers from images"""
    
    def __init__(self, method='tesseract'):
        """
        Initialize the number detector
        
        Args:
            method (str): Detection method - 'tesseract', 'easyocr', or 'both'
        """
        self.method = method
        self.setup_ocr_engines()
    
    def setup_ocr_engines(self):
        """Setup OCR engines based on selected method"""
        try:
            if self.method in ['tesseract', 'both']:
                import pytesseract
                self.pytesseract = pytesseract
                logger.info("Tesseract OCR initialized")
            
            if self.method in ['easyocr', 'both']:
                import easyocr
                self.easyocr_reader = easyocr.Reader(['en'], gpu=False)
                logger.info("EasyOCR initialized")
                
        except ImportError as e:
            logger.error(f"Failed to import OCR library: {e}")
            logger.info("Please install required packages:")
            logger.info("pip install pytesseract easyocr opencv-python pillow")
            raise
    
    def preprocess_image(self, image_path: str, enhance_contrast: bool = True) -> np.ndarray:
        """
        Preprocess image for better OCR results
        
        Args:
            image_path (str): Path to the image file
            enhance_contrast (bool): Whether to enhance contrast
            
        Returns:
            np.ndarray: Preprocessed image
        """
        # Read image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        if enhance_contrast:
            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            enhanced = clahe.apply(blurred)
        else:
            enhanced = blurred
        
        # Apply threshold to get binary image
        _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Morphological operations to clean up the image
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def detect_with_tesseract(self, image: np.ndarray) -> List[str]:
        """
        Detect numbers using Tesseract OCR
        
        Args:
            image (np.ndarray): Preprocessed image
            
        Returns:
            List[str]: List of detected numbers
        """
        try:
            # Configure Tesseract for digit recognition
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789'
            
            # Extract text
            text = self.pytesseract.image_to_string(image, config=custom_config)
            
            # Extract numbers using regex
            numbers = re.findall(r'\d+', text)
            
            logger.info(f"Tesseract detected: {numbers}")
            return numbers
            
        except Exception as e:
            logger.error(f"Tesseract detection failed: {e}")
            return []
    
    def detect_with_easyocr(self, image: np.ndarray) -> List[str]:
        """
        Detect numbers using EasyOCR
        
        Args:
            image (np.ndarray): Preprocessed image
            
        Returns:
            List[str]: List of detected numbers
        """
        try:
            # Use EasyOCR to detect text
            results = self.easyocr_reader.readtext(image, allowlist='0123456789')
            
            # Extract numbers with confidence > 0.5
            numbers = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:
                    # Extract only digits
                    digits = re.findall(r'\d+', text)
                    numbers.extend(digits)
            
            logger.info(f"EasyOCR detected: {numbers}")
            return numbers
            
        except Exception as e:
            logger.error(f"EasyOCR detection failed: {e}")
            return []
    
    def detect_numbers(self, image_path: str, save_preprocessed: bool = False) -> List[str]:
        """
        Main method to detect numbers in an image
        
        Args:
            image_path (str): Path to the image file
            save_preprocessed (bool): Whether to save preprocessed image
            
        Returns:
            List[str]: List of detected numbers
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        logger.info(f"Processing image: {image_path}")
        
        # Preprocess image
        processed_image = self.preprocess_image(image_path)
        
        # Save preprocessed image if requested
        if save_preprocessed:
            preprocessed_path = f"preprocessed_{Path(image_path).name}"
            cv2.imwrite(preprocessed_path, processed_image)
            logger.info(f"Preprocessed image saved as: {preprocessed_path}")
        
        all_numbers = []
        
        # Detect using selected method(s)
        if self.method in ['tesseract', 'both']:
            tesseract_numbers = self.detect_with_tesseract(processed_image)
            all_numbers.extend(tesseract_numbers)
        
        if self.method in ['easyocr', 'both']:
            easyocr_numbers = self.detect_with_easyocr(processed_image)
            all_numbers.extend(easyocr_numbers)
        
        # Remove duplicates while preserving order
        unique_numbers = list(dict.fromkeys(all_numbers))
        
        logger.info(f"Final detected numbers: {unique_numbers}")
        return unique_numbers
    
    def detect_numbers_with_positions(self, image_path: str) -> List[Tuple[str, Tuple]]:
        """
        Detect numbers with their positions in the image
        
        Args:
            image_path (str): Path to the image file
            
        Returns:
            List[Tuple[str, Tuple]]: List of (number, bounding_box) tuples
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        processed_image = self.preprocess_image(image_path)
        results = []
        
        if self.method in ['easyocr', 'both']:
            try:
                # EasyOCR provides bounding box information
                ocr_results = self.easyocr_reader.readtext(processed_image, allowlist='0123456789')
                
                for (bbox, text, confidence) in ocr_results:
                    if confidence > 0.5:
                        numbers = re.findall(r'\d+', text)
                        for number in numbers:
                            results.append((number, bbox))
                            
            except Exception as e:
                logger.error(f"Position detection failed: {e}")
        
        return results

def main():
    """Example usage of the NumberDetector"""
    
    # Example image path
    image_path = "a.jpg"  # Change this to your image path
    
    if not os.path.exists(image_path):
        logger.error(f"Image file '{image_path}' not found!")
        logger.info("Please make sure the image file exists in the current directory.")
        return
    
    try:
        # Method 1: Using Tesseract only
        logger.info("=== Using Tesseract OCR ===")
        detector_tesseract = NumberDetector(method='tesseract')
        numbers_tesseract = detector_tesseract.detect_numbers(image_path, save_preprocessed=True)
        print(f"Numbers detected with Tesseract: {numbers_tesseract}")
        
        # Method 2: Using EasyOCR only
        logger.info("\n=== Using EasyOCR ===")
        detector_easyocr = NumberDetector(method='easyocr')
        numbers_easyocr = detector_easyocr.detect_numbers(image_path)
        print(f"Numbers detected with EasyOCR: {numbers_easyocr}")
        
        # Method 3: Using both methods
        logger.info("\n=== Using Both Methods ===")
        detector_both = NumberDetector(method='both')
        numbers_both = detector_both.detect_numbers(image_path)
        print(f"Numbers detected with both methods: {numbers_both}")
        
        # Method 4: Detect with positions
        logger.info("\n=== Detecting with Positions ===")
        numbers_with_positions = detector_easyocr.detect_numbers_with_positions(image_path)
        print("Numbers with positions:")
        for number, bbox in numbers_with_positions:
            print(f"  Number: {number}, Position: {bbox}")
        
    except Exception as e:
        logger.error(f"Error processing image: {e}")

if __name__ == "__main__":
    main()
