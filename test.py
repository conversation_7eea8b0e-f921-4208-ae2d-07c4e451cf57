import requests
import time

headers = {
    # 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    "Host": "erp.bzte.ac.ir",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "en-US,en;q=0.5",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Authorization": "Bearer AA7E5C0ACA6CFAB107BE6030AC244DD5022AA5674F7615F983B1FB812F7EF8171F3F9D1E4D874F6D11D23C3633F21D3F9C77C3E443FD811A44E85A4E9A94DA43664D210EC7232ED0964C274893C868C194449413A27D9A153BFBD7FBD58AF638",
    "Authentication": "Bearer 51AC9B813CED369FD223097070A1447D8E3922ED38EC9EBAFE32C7544FBE01CA810816483ABDD366A0C48365CDD77C6E51412F424017F466BE676E865969E584A94A26B78144C5ED1A991256212711946D439A58B26062606C8266F03FB2CE51C660F281C8A5089170997B2A20D6E73B7E24CAAE08864DB763D571B9AB21A067",
    "mainHafmancode2": "",
    "Cache-Control": "no-cache",
    "Content-Type": "application/json",
    "Content-Length": "105",
    "Origin": "https://erp.bzte.ac.ir",
    "Connection": "keep-alive",
    "Referer": "https://erp.bzte.ac.ir/Hermes_Web/Dashboard/singin",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "Priority": "u=0"
}

proxies = {
    'http': f'127.0.0.1:8000',
    'https': f'127.0.0.1:8000'
}

# response = requests.post(
#     "https://erp.bzte.ac.ir/api/DDDC73C7DDE2953A0AF0BA9914DE2FD90B4EE64E3C2A0230D142A717D3798B38",
#     data={"userName":"4022114128",
#     "cipher":str("2710437778").strip().encode('utf-8').hex(),
#     "Rememberme":False,"device":False,"hash":None},
#     headers=headers,
#     proxies=proxies,
#     # timeout=30
# )

response = requests.get("https://erp.bzte.ac.ir/", headers=headers, proxies=proxies)

print(f"txt: {response.text}")
print(f"headers: {response.headers}")
print(f"cookies: {response.cookies}")
