#!/usr/bin/env python3
"""
Simple Number Detection in Images
Quick and easy script to detect numbers in images using Tesseract OCR
"""

import cv2
import pytesseract
import re
import os

def detect_numbers_simple(image_path):
    """
    Simple function to detect numbers in an image
    
    Args:
        image_path (str): Path to the image file
        
    Returns:
        list: List of detected numbers as strings
    """
    
    # Check if image exists
    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found!")
        return []
    
    try:
        # Read the image
        image = cv2.imread(image_path)
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply threshold to get binary image
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Configure Tesseract to only recognize digits
        custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789'
        
        # Extract text using Tesseract
        text = pytesseract.image_to_string(thresh, config=custom_config)
        
        # Find all numbers in the extracted text
        numbers = re.findall(r'\d+', text)
        
        print(f"Detected numbers: {numbers}")
        return numbers
        
    except Exception as e:
        print(f"Error processing image: {e}")
        return []

def main():
    """Main function to test the number detection"""
    
    # Image path - change this to your image file
    image_path = "a.jpg"
    
    print(f"Detecting numbers in: {image_path}")
    
    # Detect numbers
    detected_numbers = detect_numbers_simple(image_path)
    
    if detected_numbers:
        print(f"\n✅ Found {len(detected_numbers)} number(s):")
        for i, number in enumerate(detected_numbers, 1):
            print(f"  {i}. {number}")
    else:
        print("\n❌ No numbers detected in the image.")
        print("Tips:")
        print("- Make sure the image contains clear, readable numbers")
        print("- Try improving image quality or contrast")
        print("- Ensure Tesseract is properly installed")

if __name__ == "__main__":
    main()
