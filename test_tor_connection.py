#!/usr/bin/env python3
"""
Simple test script to verify Tor connection and control port configuration
"""

import time
import logging
from tor_requester import TorManager

# Configure logging to see debug info
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_tor_connection():
    """Test Tor connection step by step"""
    
    logger.info("=== Starting Tor Connection Test ===")
    
    # Initialize Tor manager
    tor_manager = TorManager(
        control_port=9051,
        socks_port=9050,
        password=None
    )
    
    try:
        # Step 1: Start Tor service
        logger.info("Step 1: Starting Tor service...")
        if not tor_manager.start_tor_service():
            logger.error("Failed to start Tor service")
            return False
        
        logger.info("✓ Tor service started successfully")
        
        # Step 2: Wait for full initialization
        logger.info("Step 2: Waiting for Tor to fully initialize...")
        time.sleep(15)  # Give Tor more time to bootstrap
        
        # Step 3: Test control connection
        logger.info("Step 3: Testing control connection...")
        if tor_manager.is_tor_running():
            logger.info("✓ Control connection working")
        else:
            logger.error("✗ Control connection failed")
            return False
        
        # Step 4: Get initial IP
        logger.info("Step 4: Getting current IP...")
        current_ip = tor_manager.get_current_ip()
        if current_ip:
            logger.info(f"✓ Current IP: {current_ip}")
        else:
            logger.error("✗ Failed to get IP through Tor")
            return False
        
        # Step 5: Test IP change (optional)
        logger.info("Step 5: Testing IP change...")
        if tor_manager.change_ip():
            logger.info("✓ IP change signal sent successfully")
            time.sleep(5)
            new_ip = tor_manager.get_current_ip()
            if new_ip:
                logger.info(f"✓ New IP: {new_ip}")
            else:
                logger.warning("Could not verify new IP")
        else:
            logger.warning("IP change failed, but this might be normal")
        
        logger.info("=== Test completed successfully ===")
        return True
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        return False
        
    finally:
        # Cleanup
        logger.info("Cleaning up...")
        tor_manager.stop_tor_service()
        logger.info("Cleanup completed")

if __name__ == "__main__":
    success = test_tor_connection()
    if success:
        print("\n🎉 All tests passed! Your Tor configuration is working.")
    else:
        print("\n❌ Some tests failed. Check the logs above for details.")
